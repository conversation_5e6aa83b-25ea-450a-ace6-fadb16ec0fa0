package com.example.castapp.ui.view

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.ViewOutlineProvider
import android.widget.FrameLayout
import com.example.castapp.model.WindowVisualizationData
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.RemoteTextFormatParser
import kotlin.math.roundToInt

/**
 * 🪟 单个窗口容器可视化View
 * 使用View.clipBounds实现裁剪，与接收端CropManager保持一致
 * 🎯 修复：改为FrameLayout以支持裁剪覆盖层
 */
class WindowVisualizationContainerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    // 窗口数据
    private var windowData: WindowVisualizationData? = null

    // 🎯 新增：裁剪模式相关
    var cropOverlay: CropOverlayView? = null // 公开访问，供父容器使用
        private set
    private var isCropping = false
    private var cropModeCallback: ((RectF?, Boolean) -> Unit)? = null

    // 🎯 修复：使用WeakReference避免内存泄漏和并发问题
    private var borderViewRef: java.lang.ref.WeakReference<View>? = null
    private var isDetached = false

    // 🎯 边框状态保存（用于裁剪模式恢复，参考接收端行为）
    private var borderStateBeforeCrop = false

    // 📝 新增：TextView控件用于显示文字内容
    private var textView: android.widget.TextView? = null
    private var currentTextContent: String = ""

    // 🎯 新增：富文本格式解析器
    private val textFormatParser = RemoteTextFormatParser(context)

    /**
     * 🎯 智能边框：可动态调整的边框View
     */
    private inner class DynamicBorderView(context: Context, private var borderData: WindowVisualizationData) : View(context) {
        private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        private val borderRect = RectF()

        init {
            updateBorderPaint()
        }

        fun updateBorderData(newData: WindowVisualizationData) {
            borderData = newData
            updateBorderPaint()
            invalidate() // 触发重绘
        }

        private fun updateBorderPaint() {
            borderPaint.apply {
                style = Paint.Style.STROKE
                strokeWidth = dpToPx(borderData.borderWidth)
                color = borderData.borderColor
            }
        }

        override fun onDraw(canvas: Canvas) {
            super.onDraw(canvas)

            val borderWidthPx = dpToPx(borderData.borderWidth)
            val cornerRadiusPx = dpToPx(borderData.cornerRadius)

            // 设置绘制区域（基于当前View的实际尺寸）
            borderRect.set(
                borderWidthPx / 2f,
                borderWidthPx / 2f,
                width - borderWidthPx / 2f,
                height - borderWidthPx / 2f
            )

            // 边框圆角半径需要加上边框宽度的一半
            val borderRadius = cornerRadiusPx + borderWidthPx / 2f
            canvas.drawRoundRect(borderRect, borderRadius, borderRadius, borderPaint)

            AppLog.v("【智能边框】边框已绘制: ${borderData.getShortConnectionId()}, 尺寸=${width}×${height}")
        }
    }

    /**
     * 设置窗口数据并更新显示
     */
    fun setWindowData(data: WindowVisualizationData) {
        setWindowDataInternal(data, forceRefresh = false)
    }

    /**
     * 🎯 获取窗口数据
     */
    fun getWindowData(): WindowVisualizationData? = windowData

    /**
     * 内部窗口数据设置方法，支持强制刷新
     */
    private fun setWindowDataInternal(data: WindowVisualizationData, forceRefresh: Boolean = false) {
        this.windowData = data

        // 🎯 关键：使用View.clipBounds实现裁剪，与接收端保持一致
        applyClipBounds(data)

        // 设置View的位置和尺寸
        val bounds = data.getVisualizationBounds()

        // 🎯 修复：确保layoutParams不为null
        if (layoutParams == null) {
            layoutParams = FrameLayout.LayoutParams(
                bounds.width().roundToInt(),
                bounds.height().roundToInt()
            )
        } else {
            layoutParams = layoutParams.apply {
                width = bounds.width().roundToInt()
                height = bounds.height().roundToInt()
            }
        }

        // 设置位置
        x = bounds.left
        y = bounds.top

        // 设置旋转
        rotation = data.rotationAngle

        // 设置透明度
        alpha = data.alpha

        invalidate()

        AppLog.d("【窗口容器View】设置窗口数据: ${data.getShortConnectionId()}")
        AppLog.d("  位置: (${bounds.left}, ${bounds.top})")
        AppLog.d("  尺寸: ${bounds.width().roundToInt()}×${bounds.height().roundToInt()}")
        AppLog.d("  裁剪状态: ${data.cropRectRatio != null}")
        AppLog.d("  镜像状态: ${data.isMirrored}")
        AppLog.d("  文字内容: $currentTextContent")

        // 🎯 添加边框状态调试日志
        AppLog.d("【窗口容器View】边框状态:")
        AppLog.d("  边框启用: ${data.isBorderEnabled}")
        AppLog.d("  边框颜色: ${String.format("#%08X", data.borderColor)}")
        AppLog.d("  边框宽度: ${data.borderWidth}dp")

        // 📝 新增：只对文字窗口创建或更新TextView控件
        if (isTextWindow(data.connectionId)) {
            setupTextView(data)
        }

        // 🎯 关键修复：使用独立边框View，与接收端保持一致
        updateBorderView(data)

        // 强制刷新（用于裁剪模式的完整重建）
        if (forceRefresh) {
            invalidate()
            requestLayout()

            // 刷新所有子View
            for (i in 0 until childCount) {
                getChildAt(i)?.let { child ->
                    child.invalidate()
                    child.requestLayout()
                }
            }

            AppLog.d("【窗口容器View】强制刷新完成: ${data.getShortConnectionId()}")
        }
    }
    
    /**
     * 🎯 应用View.clipBounds裁剪，与接收端CropManager保持一致
     */
    private fun applyClipBounds(data: WindowVisualizationData) {
        val cropRatio = data.cropRectRatio
        // 🎯 统一圆角半径处理：所有窗口都使用固定圆角半径，不受缩放因子影响
        val cornerRadiusPx = dpToPx(data.cornerRadius)

        if (cropRatio != null) {
            // 🎯 统一方案：直接设置View的clipBounds（与接收端CropManager相同）
            val bounds = data.getVisualizationBounds()
            val clipBounds = Rect(
                (cropRatio.left * bounds.width()).toInt(),
                (cropRatio.top * bounds.height()).toInt(),
                (cropRatio.right * bounds.width()).toInt(),
                (cropRatio.bottom * bounds.height()).toInt()
            )

            this.clipBounds = clipBounds

            // 🎯 关键修复：裁剪状态下同时使用clipBounds和圆角裁剪（与接收端保持一致）
            // clipBounds负责矩形区域裁剪，outline负责圆角裁剪
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    // 🎯 关键：基于clipBounds区域设置圆角outline
                    outline.setRoundRect(
                        clipBounds.left,
                        clipBounds.top,
                        clipBounds.right,
                        clipBounds.bottom,
                        cornerRadiusPx
                    )
                }
            }
            clipToOutline = true // 🎯 启用圆角裁剪

            AppLog.d("【窗口容器View】应用View.clipBounds裁剪: ${data.getShortConnectionId()}")
            AppLog.d("  裁剪比例: left=${cropRatio.left}, top=${cropRatio.top}, right=${cropRatio.right}, bottom=${cropRatio.bottom}")
            AppLog.d("  裁剪区域: $clipBounds")
            AppLog.d("  🎯 统一圆角半径: ${data.cornerRadius}dp (固定) = ${cornerRadiusPx}px")
        } else {
            // 清除裁剪，但保持圆角
            this.clipBounds = null

            // 🎯 未裁剪状态：设置基于整个容器的圆角outline
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, cornerRadiusPx)
                }
            }
            clipToOutline = true

            AppLog.d("【窗口容器View】移除裁剪: ${data.getShortConnectionId()}")
            AppLog.d("  🎯 统一圆角半径: ${data.cornerRadius}dp (固定) = ${cornerRadiusPx}px")
        }
    }
    
    init {
        // 🎯 设置为可绘制，因为FrameLayout默认不绘制
        setWillNotDraw(false)
    }

    /**
     * 判断是否为文字窗口
     */
    private fun isTextWindow(connectionId: String): Boolean {
        return connectionId.startsWith("text_")
    }

    /**
     * 判断是否为摄像头占位容器
     * 🎯 新增：识别摄像头占位容器，用于显示占位内容
     */
    private fun isCameraPlaceholder(connectionId: String): Boolean {
        return connectionId.startsWith("front_camera_") || connectionId.startsWith("rear_camera_")
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        // 🎯 修复：容器被添加到父容器后，等待布局完成再创建边框
        val data = windowData
        if (data != null && data.isBorderEnabled) {
            AppLog.d("【窗口容器View】已附加到窗口，等待布局完成后创建边框")
            // 使用OnLayoutChangeListener等待布局完成
            addOnLayoutChangeListener(object : OnLayoutChangeListener {
                override fun onLayoutChange(
                    v: View?, left: Int, top: Int, right: Int, bottom: Int,
                    oldLeft: Int, oldTop: Int, oldRight: Int, oldBottom: Int
                ) {
                    // 移除监听器，避免重复调用
                    removeOnLayoutChangeListener(this)

                    // 检查尺寸是否有效
                    val width = right - left
                    val height = bottom - top
                    if (width > 0 && height > 0) {
                        AppLog.d("【窗口容器View】布局完成，容器尺寸: ${width}×${height}，创建边框")
                        updateBorderView(data)
                    } else {
                        AppLog.w("【窗口容器View】布局完成但尺寸仍无效: ${width}×${height}")
                    }
                }
            })
        }
    }

    override fun onDetachedFromWindow() {
        // 🎯 关键修复：在容器分离前立即清理边框，避免延迟清理失败
        AppLog.d("【窗口容器View】容器即将分离，立即清理边框")
        safeRemoveBorderView()

        super.onDetachedFromWindow()
        // 🎯 修复：标记为已分离，避免后续操作
        isDetached = true

        AppLog.d("【窗口容器View】已从窗口分离，边框已清理")
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val data = windowData ?: return

        // 绘制窗口内容（不需要Canvas.clipRect，因为已经使用View.clipBounds）
        drawWindowContent(canvas, data)
    }
    
    /**
     * 绘制窗口内容
     */
    private fun drawWindowContent(canvas: Canvas, data: WindowVisualizationData) {
        val bounds = RectF(0f, 0f, width.toFloat(), height.toFloat())

        // 🎯 修复：移除所有背景填充绘制，保持完全透明
        AppLog.d("【窗口容器View】🎨 跳过所有背景填充绘制，保持透明: ${data.getShortConnectionId()}")

        // 🎯 根据窗口类型选择显示方式
        if (isTextWindow(data.connectionId)) {
            // 📝 文字窗口：使用TextView控件显示，无需Canvas绘制
            AppLog.d("【窗口容器View】📝 文字窗口由TextView控件显示: ${data.getShortConnectionId()}")
        } else if (isCameraPlaceholder(data.connectionId)) {
            // 📷 摄像头占位容器：绘制占位内容
            drawCameraPlaceholder(canvas, bounds, data)
        } else {
            // 📸 其他窗口类型：使用截图显示
            data.screenshotBitmap?.let { bitmap ->
                drawScreenshot(canvas, bitmap, bounds, data)
            }
        }
    }

    /**
     * 绘制截图（非文字窗口使用）
     */
    private fun drawScreenshot(canvas: Canvas, bitmap: Bitmap, bounds: RectF, data: WindowVisualizationData) {
        val paint = Paint(Paint.ANTI_ALIAS_FLAG or Paint.FILTER_BITMAP_FLAG)

        // 计算截图的绘制区域（保持宽高比）
        val bitmapAspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
        val boundsAspectRatio = bounds.width() / bounds.height()

        val drawRect = if (bitmapAspectRatio > boundsAspectRatio) {
            // 截图更宽，以高度为准
            val drawWidth = bounds.height() * bitmapAspectRatio
            val offsetX = (bounds.width() - drawWidth) / 2f
            RectF(bounds.left + offsetX, bounds.top, bounds.left + offsetX + drawWidth, bounds.bottom)
        } else {
            // 截图更高，以宽度为准
            val drawHeight = bounds.width() / bitmapAspectRatio
            val offsetY = (bounds.height() - drawHeight) / 2f
            RectF(bounds.left, bounds.top + offsetY, bounds.right, bounds.top + offsetY + drawHeight)
        }

        // 🎯 修复：如果窗口开启了镜像，需要对截图应用镜像变换
        if (data.isMirrored) {
            canvas.save()

            // 计算镜像变换：以绘制区域中心为轴进行水平镜像
            val centerX = drawRect.centerX()
            canvas.translate(centerX, 0f)  // 移动到中心
            canvas.scale(-1f, 1f)          // 水平镜像
            canvas.translate(-centerX, 0f) // 移回原位置

            canvas.drawBitmap(bitmap, null, drawRect, paint)
            canvas.restore()

            AppLog.v("【窗口容器View】截图已应用镜像变换: ${data.getShortConnectionId()}")
        } else {
            canvas.drawBitmap(bitmap, null, drawRect, paint)
        }
    }

    /**
     * 🎯 新增：开始裁剪模式
     */
    fun startCropMode(originalCropRatio: RectF?, callback: (RectF?, Boolean) -> Unit) {
        if (isCropping) {
            AppLog.d("【窗口容器View】已经在裁剪模式中")
            return
        }

        isCropping = true
        cropModeCallback = callback

        // 参考接收端CropManager.startCropMode()逻辑，完整清理裁剪状态
        val currentData = windowData
        if (currentData != null && currentData.cropRectRatio != null) {
            // 如果当前有裁剪，临时清除裁剪以显示完整画面进行重新裁剪
            AppLog.d("【窗口容器View】临时清除裁剪显示，恢复完整画面")

            // 完全清除裁剪状态
            clipBounds = null
            clipToOutline = false
            outlineProvider = null

            // 重新设置窗口数据，触发完整的重建过程
            val tempData = currentData.copy(
                cropRectRatio = null,
                isCropping = false
            )
            setWindowDataInternal(tempData, forceRefresh = true)

            AppLog.d("【窗口容器View】完整清理裁剪状态完成")
        } else {
            // 没有裁剪状态，只需要基本清理
            clipBounds = null
            clipToOutline = false
            outlineProvider = null

            AppLog.d("【窗口容器View】基本清理完成")
        }

        // 🎯 修复：参考接收端行为，进入裁剪模式时完全移除边框View
        if (currentData != null && currentData.isBorderEnabled) {
            // 保存边框状态，用于退出裁剪模式时恢复
            borderStateBeforeCrop = true
            removeBorderView()
            AppLog.d("【窗口容器View】进入裁剪模式，移除边框View（参考接收端行为）")
        } else {
            borderStateBeforeCrop = false
            AppLog.d("【窗口容器View】进入裁剪模式，当前无边框")
        }

        // 🎯 同时清除圆角裁剪效果，恢复到完整窗口显示
        if (currentData != null) {
            // 🎯 统一圆角半径处理：所有窗口都使用固定圆角半径
            val cornerRadiusPx = dpToPx(currentData.cornerRadius)
            // 设置基于整个容器的圆角outline（无裁剪状态）
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, cornerRadiusPx)
                }
            }
            clipToOutline = true
            AppLog.d("【窗口容器View】临时清除裁剪效果，恢复完整画面显示")
        }

        // 🎯 修复：创建简化的裁剪覆盖层，只负责裁剪框，不包含按钮
        cropOverlay = CropOverlayView(context).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            // 如果有原始裁剪区域，设置为初始裁剪框位置
            originalCropRatio?.let { ratio ->
                setCropRectRatio(ratio)
                AppLog.d("【窗口容器View】恢复之前的裁剪框位置: 比例: $ratio")
            }

            // 设置裁剪变化监听器
            setCropChangeListener(object : CropOverlayView.CropChangeListener {
                override fun onCropChanged(cropRect: RectF) {
                    // 实时更新裁剪区域
                    AppLog.v("【窗口容器View】裁剪区域变化: $cropRect")
                }
            })
        }

        // 添加裁剪覆盖层到窗口容器中
        addView(cropOverlay)
        cropOverlay?.bringToFront()

        AppLog.d("【窗口容器View】进入裁剪模式")
    }

    /**
     * 🎯 新增：结束裁剪模式
     */
    fun endCropMode(isCancel: Boolean) {
        if (!isCropping) return

        val finalCropRatio = if (!isCancel) {
            cropOverlay?.getCropRectRatio()
        } else {
            null
        }

        AppLog.d("【窗口容器View】结束裁剪模式，取消: $isCancel")
        AppLog.d("【窗口容器View】最终裁剪比例: $finalCropRatio")

        // 移除裁剪覆盖层
        cropOverlay?.let { overlay ->
            removeView(overlay)
        }
        cropOverlay = null

        // 🎯 裁剪修复：退出裁剪模式时不立即重建边框View
        // 边框View将在后续的setWindowData()调用中统一重建，避免重复创建
        if (borderStateBeforeCrop) {
            AppLog.d("【裁剪修复】退出裁剪模式，边框将在数据更新时重建")
        } else {
            AppLog.d("【裁剪修复】退出裁剪模式，进入前无边框，保持无边框状态")
        }

        // 🎯 重置边框状态标记，避免状态混乱
        borderStateBeforeCrop = false

        isCropping = false

        // 🎯 关键修复：如果不是取消操作，需要立即应用最终的裁剪效果
        if (!isCancel && finalCropRatio != null) {
            // 立即应用裁剪效果到View.clipBounds
            applyFinalCropBounds(finalCropRatio)
            AppLog.d("【窗口容器View】立即应用最终裁剪效果: $finalCropRatio")
        }

        // 回调结果
        cropModeCallback?.invoke(finalCropRatio, isCancel)
        cropModeCallback = null
    }

    /**
     * 🎯 新增：应用最终裁剪边界
     */
    private fun applyFinalCropBounds(cropRatio: RectF) {
        val data = windowData ?: return
        val bounds = data.getVisualizationBounds()
        // 🎯 统一圆角半径处理：所有窗口都使用固定圆角半径
        val cornerRadiusPx = dpToPx(data.cornerRadius)

        val clipBounds = Rect(
            (cropRatio.left * bounds.width()).toInt(),
            (cropRatio.top * bounds.height()).toInt(),
            (cropRatio.right * bounds.width()).toInt(),
            (cropRatio.bottom * bounds.height()).toInt()
        )

        this.clipBounds = clipBounds

        // 🎯 关键修复：同时应用圆角效果
        outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(
                    clipBounds.left,
                    clipBounds.top,
                    clipBounds.right,
                    clipBounds.bottom,
                    cornerRadiusPx
                )
            }
        }
        clipToOutline = true

        AppLog.d("【窗口容器View】应用最终裁剪边界: $clipBounds")
        AppLog.d("【窗口容器View】🎯 统一圆角效果: ${data.cornerRadius}dp (固定) = ${cornerRadiusPx}px")
    }

    /**
     * 🎯 智能边框管理：动态调整现有边框或按需创建
     */
    private fun updateBorderView(data: WindowVisualizationData) {
        val parentView = parent as? ViewGroup
        if (parentView == null) {
            AppLog.w("【窗口容器View】无法获取父容器，跳过边框操作")
            return
        }

        // 🎯 检查容器尺寸是否有效
        if (width <= 0 || height <= 0) {
            AppLog.w("【窗口容器View】容器尺寸无效(${width}×${height})，跳过边框操作")
            return
        }

        AppLog.d("【窗口容器View】智能更新边框: 边框启用=${data.isBorderEnabled}, 容器尺寸=${width}×${height}")

        if (data.isBorderEnabled) {
            // 需要显示边框：优先调整现有边框，不存在时才创建
            val existingBorderView = borderViewRef?.get()
            if (existingBorderView != null && existingBorderView.parent != null) {
                // 边框已存在，动态调整尺寸和位置
                adjustExistingBorderView(existingBorderView, data)
                AppLog.d("【智能边框】已调整现有边框尺寸，避免重建")
            } else {
                // 边框不存在，创建新边框
                cleanupExistingBorder() // 清理可能的残留
                createBorderView(data, parentView)
                AppLog.d("【智能边框】创建新边框")
            }
        } else {
            // 不需要显示边框：清理现有边框
            cleanupExistingBorder()
        }
    }

    /**
     * 🎯 智能边框管理：动态调整现有边框尺寸和位置
     */
    private fun adjustExistingBorderView(borderView: View, data: WindowVisualizationData) {
        try {
            val borderWidthPx = dpToPx(data.borderWidth).toInt()

            // 🎯 关键：根据是否裁剪计算新的边框位置和尺寸
            val (borderLeft, borderTop, borderWidth, borderHeight) = if (data.cropRectRatio != null && clipBounds != null) {
                // 裁剪状态：基于clipBounds计算边框位置
                val left = clipBounds.left - borderWidthPx
                val top = clipBounds.top - borderWidthPx
                val width = clipBounds.width() + borderWidthPx * 2
                val height = clipBounds.height() + borderWidthPx * 2
                arrayOf(left, top, width, height)
            } else {
                // 未裁剪状态：基于整个容器计算边框位置
                val left = -borderWidthPx
                val top = -borderWidthPx
                val width = this.width + borderWidthPx * 2
                val height = this.height + borderWidthPx * 2
                arrayOf(left, top, width, height)
            }

            // 🎯 智能更新：如果是DynamicBorderView，更新其数据
            if (borderView is DynamicBorderView) {
                borderView.updateBorderData(data)
            }

            // 🎯 动态调整边框View的布局参数
            val layoutParams = borderView.layoutParams as? FrameLayout.LayoutParams
            if (layoutParams != null) {
                layoutParams.width = borderWidth
                layoutParams.height = borderHeight
                layoutParams.leftMargin = borderLeft
                layoutParams.topMargin = borderTop
                borderView.layoutParams = layoutParams

                // 强制重新布局和绘制
                borderView.requestLayout()
                borderView.invalidate()

                AppLog.d("【智能边框】边框尺寸已调整: 位置=($borderLeft, $borderTop), 尺寸=${borderWidth}×${borderHeight}")
            } else {
                AppLog.w("【智能边框】无法获取边框布局参数，回退到重建模式")
                // 回退到重建模式
                cleanupExistingBorder()
                createBorderView(data, parent as ViewGroup)
            }

        } catch (e: Exception) {
            AppLog.e("【智能边框】调整边框尺寸失败，回退到重建模式", e)
            // 出错时回退到重建模式
            cleanupExistingBorder()
            createBorderView(data, parent as ViewGroup)
        }
    }

    /**
     * 🎯 优雅边框管理：简洁的边框清理
     */
    private fun cleanupExistingBorder() {
        // 清理WeakReference中的边框
        safeRemoveBorderView()

        // 清理可能的残留边框（通过tag识别）
        val parentView = parent as? ViewGroup
        val windowData = this.windowData
        if (parentView != null && windowData != null) {
            val targetTag = "BorderView_${windowData.connectionId}"
            val borderViewsToRemove = mutableListOf<View>()

            for (i in 0 until parentView.childCount) {
                val child = parentView.getChildAt(i)
                if (child != this && child.tag == targetTag) {
                    borderViewsToRemove.add(child)
                }
            }

            borderViewsToRemove.forEach { view ->
                parentView.removeView(view)
                AppLog.d("【边框清理】移除残留边框View: tag=${view.tag}")
            }

            if (borderViewsToRemove.isNotEmpty()) {
                AppLog.d("【边框清理】清理完成，移除了 ${borderViewsToRemove.size} 个边框View")
            }
        }
    }

    /**
     * 🎯 优雅边框管理：创建独立边框View
     */
    private fun createBorderView(data: WindowVisualizationData, parentView: ViewGroup) {
        // 🎯 修复重复缩放问题：边框宽度保持原始值，不预先应用缩放因子
        // 缩放将通过容器的scaleX/scaleY统一应用，避免双重缩放
        val borderWidthPx = dpToPx(data.borderWidth).toInt()

        // 🎯 关键：根据是否裁剪计算边框位置和尺寸（与接收端逻辑一致）
        val (borderLeft, borderTop, borderWidth, borderHeight) = if (data.cropRectRatio != null && clipBounds != null) {
            // 裁剪状态：基于clipBounds计算边框位置
            val left = clipBounds.left - borderWidthPx
            val top = clipBounds.top - borderWidthPx
            val width = clipBounds.width() + borderWidthPx * 2
            val height = clipBounds.height() + borderWidthPx * 2
            arrayOf(left, top, width, height)
        } else {
            // 未裁剪状态：基于整个容器计算边框位置
            val left = -borderWidthPx
            val top = -borderWidthPx
            val width = this.width + borderWidthPx * 2
            val height = this.height + borderWidthPx * 2
            arrayOf(left, top, width, height)
        }

        // 🎯 修复：检查是否已分离，避免在分离过程中创建新View
        if (isDetached) {
            AppLog.w("【窗口容器View】容器已分离，跳过边框创建")
            return
        }

        // 🎯 智能边框：创建可动态调整的边框View
        val newBorderView = DynamicBorderView(context, data).apply {
            // 🎯 裁剪修复：为边框View添加标记，便于识别和清理
            tag = "BorderView_${data.connectionId}"
            // 设置布局参数
            layoutParams = FrameLayout.LayoutParams(borderWidth, borderHeight).apply {
                leftMargin = borderLeft
                topMargin = borderTop
            }

            // 🎯 关键：边框View与容器使用相同的坐标系统
            x = this@WindowVisualizationContainerView.x
            y = this@WindowVisualizationContainerView.y
            translationX = <EMAIL>
            translationY = <EMAIL>
            scaleX = <EMAIL>
            scaleY = <EMAIL>
            rotation = <EMAIL>

            // 🎯 关键修复：设置pivot点，确保旋转时边框与容器保持贴合
            // 边框视图pivot点 = 容器pivot点 - 边框视图的layout偏移（与接收端逻辑一致）
            pivotX = <EMAIL> - borderLeft
            pivotY = <EMAIL> - borderTop

            // 设置背景透明
            setBackgroundColor(Color.TRANSPARENT)
        }

        // 🎯 优雅边框管理：直接添加边框View，无需复杂延迟
        try {
            // 检查容器是否已分离
            if (isDetached) {
                AppLog.w("【边框创建】容器已分离，跳过边框创建")
                return
            }

            // 检查边框View是否已有父容器
            if (newBorderView.parent != null) {
                AppLog.w("【边框创建】边框View已有父容器，先移除: ${newBorderView.parent}")
                (newBorderView.parent as? ViewGroup)?.removeView(newBorderView)
            }

            // 直接添加边框View到父容器
            val containerIndex = parentView.indexOfChild(this@WindowVisualizationContainerView)
            if (containerIndex >= 0) {
                // 将边框View插入到窗口容器的紧邻位置
                parentView.addView(newBorderView, containerIndex + 1)
                AppLog.d("【边框创建】边框View已创建: ${data.getShortConnectionId()}, 容器索引=$containerIndex")
            } else {
                // 如果找不到容器索引，使用默认添加方式
                parentView.addView(newBorderView)
                AppLog.d("【边框创建】边框View已创建（默认位置）: ${data.getShortConnectionId()}")
            }

            // 🎯 存储边框View引用
            borderViewRef = java.lang.ref.WeakReference(newBorderView)

            AppLog.d("  边框位置: ($borderLeft, $borderTop)")
            AppLog.d("  边框尺寸: ${borderWidth}×${borderHeight}")
            AppLog.d("  边框颜色: ${String.format("#%08X", data.borderColor)}")

        } catch (e: Exception) {
            AppLog.e("【边框创建】创建边框View时发生异常", e)
            // 清理失败的边框View
            try {
                if (newBorderView.parent != null) {
                    (newBorderView.parent as? ViewGroup)?.removeView(newBorderView)
                }
            } catch (cleanupException: Exception) {
                AppLog.e("【边框创建】清理失败边框View时发生异常", cleanupException)
            }
        }
    }

    /**
     * 🎯 修复：安全移除边框View
     */
    private fun safeRemoveBorderView() {
        val borderView = borderViewRef?.get()
        if (borderView != null) {
            try {
                // 🎯 关键修复：直接从边框View的父容器中移除，不依赖容器的parent
                val borderParent = borderView.parent as? ViewGroup
                if (borderParent != null) {
                    borderParent.removeView(borderView)
                    AppLog.d("【窗口容器View】边框View已安全移除")
                } else {
                    AppLog.d("【窗口容器View】边框View已不在父容器中")
                }
            } catch (e: Exception) {
                AppLog.w("【窗口容器View】移除边框View时发生异常", e)
            }
        } else {
            AppLog.d("【窗口容器View】边框View引用为空，无需清理")
        }
        borderViewRef = null
    }

    /**
     * 🎯 兼容：保留原有接口
     */
    private fun removeBorderView() {
        safeRemoveBorderView()
    }



    /**
     * 🎯 层级修复：重写bringToFront方法，确保边框View同步调整层级
     */
    override fun bringToFront() {
        // 首先调用父类方法，将窗口容器移到前台
        super.bringToFront()

        // 然后将边框View也移到前台，确保边框始终在窗口上方
        bringBorderToFront()

        val data = windowData
        if (data != null) {
            AppLog.d("【层级修复】可视化窗口和边框已同步调整到前台: ${data.getShortConnectionId()}")
        }
    }

    /**
     * 🎯 层级修复：将边框View移到前台（层级管理时调用）
     */
    private fun bringBorderToFront() {
        val borderView = borderViewRef?.get() ?: return
        val parentView = parent as? ViewGroup ?: return

        try {
            // 🎯 修复边框View重复添加：检查边框View是否仍在父容器中
            if (borderView.parent != parentView) {
                AppLog.w("【层级修复】边框View不在预期的父容器中，跳过层级调整")
                return
            }

            // 🎯 关键修复：将边框View重新插入到窗口容器的下一个位置
            // 先移除边框View
            parentView.removeView(borderView)

            // 获取窗口容器的当前索引
            val containerIndex = parentView.indexOfChild(this)
            if (containerIndex >= 0) {
                // 检查边框View是否已有父容器（安全检查）
                if (borderView.parent != null) {
                    AppLog.w("【层级修复】边框View在移除后仍有父容器，强制清理")
                    (borderView.parent as? ViewGroup)?.removeView(borderView)
                }

                // 将边框View重新插入到窗口容器的下一个位置
                parentView.addView(borderView, containerIndex + 1)
                AppLog.d("【层级修复】边框View已重新插入到窗口容器的下一个位置: 容器索引=$containerIndex, 边框索引=${containerIndex + 1}")
            } else {
                // 如果找不到容器索引，回退到原有方式
                parentView.addView(borderView)
                AppLog.w("【层级修复】未找到窗口容器索引，使用默认添加方式")
            }
        } catch (e: Exception) {
            AppLog.e("【层级修复】调整边框View层级时发生异常", e)
            // 清理异常状态的边框View引用
            borderViewRef = null
        }
    }

    /**
     * dp转px工具方法
     */
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, resources.displayMetrics)
    }

    // ========== 📝 TextView文字内容显示相关方法 ==========

    /**
     * 设置或更新TextView控件
     */
    private fun setupTextView(data: WindowVisualizationData) {
        // 如果TextView不存在，创建新的
        if (textView == null) {
            createTextView(data)
        } else {
            // 更新现有TextView的样式和内容
            updateTextView(data)
        }
    }

    /**
     * 创建TextView控件
     */
    private fun createTextView(data: WindowVisualizationData) {
        // 🎯 关键修复：使用TextWindowView而不是普通TextView，确保布局计算一致性
        textView = com.example.castapp.ui.view.TextWindowView(context).apply {
            // 设置布局参数，填满整个容器
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )

            // 设置tag为连接ID，用于后续查找
            tag = data.connectionId

            // 🎯 关键修复：不进入编辑模式，保持显示状态
            // 这样可以确保与编辑模式时的布局计算一致，但不显示光标和键盘

            // 🎯 关键修复：直接设置空文本，避免显示"默认文字"
            setText("")

            AppLog.d("【窗口容器View】📝 TextWindowView已创建: ${data.getShortConnectionId()}")
        }

        // 添加到容器中
        addView(textView)
        AppLog.d("【窗口容器View】📝 TextWindowView已添加到容器: ${data.getShortConnectionId()}")
    }

    /**
     * 更新TextView控件（支持富文本格式）
     */
    private fun updateTextView(data: WindowVisualizationData) {
        textView?.let { tv ->
            try {
                // 🎯 构建格式数据用于解析
                val formatData = mutableMapOf<String, Any>()
                formatData["textContent"] = data.textContent
                data.richTextData?.let { formatData["richTextData"] = it }
                formatData["isBold"] = data.isBold
                formatData["isItalic"] = data.isItalic
                formatData["fontSize"] = data.fontSize
                data.fontName?.let { formatData["fontName"] = it }
                data.fontFamily?.let { formatData["fontFamily"] = it }
                formatData["lineSpacing"] = data.lineSpacing
                formatData["textAlignment"] = data.textAlignment
                formatData["isWindowColorEnabled"] = data.isWindowColorEnabled
                formatData["windowBackgroundColor"] = data.windowBackgroundColor

                // 🎯 解析富文本格式
                val parsedFormat = textFormatParser.parseTextFormat(formatData)

                // 🎯 应用富文本格式
                if (parsedFormat.hasRichTextFormat && parsedFormat.spannableString != null) {
                    // 使用富文本格式
                    tv.text = parsedFormat.spannableString
                    AppLog.d("【窗口容器View】📝 已应用富文本格式: ${data.getShortConnectionId()}")
                    AppLog.d("【窗口容器View】📝 TextView文本设置为: '${tv.text}', 类型: ${tv.text::class.simpleName}")

                    // 验证TextView中的格式
                    if (tv.text is android.text.Spanned) {
                        val spanned = tv.text as android.text.Spanned
                        val spans = spanned.getSpans(0, spanned.length, Any::class.java)
                        AppLog.d("【窗口容器View】📝 TextView中的Span数量: ${spans.size}")
                    }
                } else {
                    // 使用基本格式
                    tv.text = parsedFormat.textContent

                    // 应用基本格式属性
                    tv.textSize = parsedFormat.fontSize.toFloat()
                    tv.setTypeface(tv.typeface,
                        when {
                            parsedFormat.isBold && parsedFormat.isItalic -> android.graphics.Typeface.BOLD_ITALIC
                            parsedFormat.isBold -> android.graphics.Typeface.BOLD
                            parsedFormat.isItalic -> android.graphics.Typeface.ITALIC
                            else -> android.graphics.Typeface.NORMAL
                        }
                    )

                    AppLog.d("【窗口容器View】📝 已应用基本格式: ${data.getShortConnectionId()}, 字号=${parsedFormat.fontSize}sp")
                }

                // 🎯 应用TextView级别的属性
                // 🎯 修复：行间距应用逻辑，支持0值和负值
                val lineSpacingExtra = parsedFormat.lineSpacing * tv.resources.displayMetrics.density
                tv.setLineSpacing(lineSpacingExtra, 1.0f)
                AppLog.d("【窗口容器View】📝 已应用行间距: ${parsedFormat.lineSpacing}dp -> ${lineSpacingExtra}px")

                // 🎯 修复：文本对齐应用
                tv.gravity = parsedFormat.textAlignment
                val alignmentName = when (parsedFormat.textAlignment) {
                    android.view.Gravity.LEFT -> "LEFT"
                    android.view.Gravity.CENTER -> "CENTER"
                    android.view.Gravity.RIGHT -> "RIGHT"
                    android.view.Gravity.CENTER_HORIZONTAL -> "CENTER_HORIZONTAL"
                    else -> "UNKNOWN(${parsedFormat.textAlignment})"
                }
                AppLog.d("【窗口容器View】📝 已应用文本对齐: ${parsedFormat.textAlignment} ($alignmentName)")

                // 🎯 新增：应用窗口背景颜色
                if (parsedFormat.isWindowColorEnabled) {
                    <EMAIL>(parsedFormat.windowBackgroundColor)
                    AppLog.d("【窗口容器View】📝 已应用窗口背景颜色: ${String.format("#%08X", parsedFormat.windowBackgroundColor)}")
                } else {
                    <EMAIL>(android.graphics.Color.TRANSPARENT)
                    AppLog.d("【窗口容器View】📝 已设置窗口背景为透明")
                }

                // 更新当前文字内容
                currentTextContent = parsedFormat.textContent

                // 应用镜像变换（如果需要）
                if (data.isMirrored) {
                    tv.scaleX = -1f
                    AppLog.d("【窗口容器View】📝 TextView已应用镜像变换: ${data.getShortConnectionId()}")
                } else {
                    tv.scaleX = 1f
                }

                AppLog.d("【窗口容器View】📝 TextView格式更新完成: ${data.getShortConnectionId()}, 富文本=${parsedFormat.hasRichTextFormat}")

            } catch (e: Exception) {
                AppLog.e("【窗口容器View】📝 更新TextView格式失败: ${data.getShortConnectionId()}", e)

                // 后备方案：仅设置纯文本
                tv.text = data.textContent
                currentTextContent = data.textContent
            }
        }
    }

    /**
     * 绘制摄像头占位内容
     * 🎯 新增：为摄像头占位容器绘制图标和文字
     */
    private fun drawCameraPlaceholder(canvas: Canvas, bounds: RectF, data: WindowVisualizationData) {
        try {
            // 设置半透明黑色背景
            val backgroundPaint = Paint().apply {
                color = 0xE0000000.toInt() // 半透明黑色
                style = Paint.Style.FILL
            }
            canvas.drawRect(bounds, backgroundPaint)

            // 计算中心位置
            val centerX = bounds.centerX()
            val centerY = bounds.centerY()

            // 绘制摄像头图标（使用emoji）
            val iconPaint = Paint().apply {
                color = 0xFFFFFFFF.toInt() // 白色
                textSize = Math.min(bounds.width(), bounds.height()) * 0.2f // 图标大小为容器的20%
                textAlign = Paint.Align.CENTER
                isAntiAlias = true
                setShadowLayer(4f, 2f, 2f, 0xFF000000.toInt()) // 添加阴影
            }

            val cameraIcon = if (data.connectionId.startsWith("front_camera_")) "📷" else "📹"
            canvas.drawText(cameraIcon, centerX, centerY - bounds.height() * 0.1f, iconPaint)

            // 绘制摄像头名称
            val namePaint = Paint().apply {
                color = 0xFFFFFFFF.toInt() // 白色
                textSize = Math.min(bounds.width(), bounds.height()) * 0.08f // 文字大小为容器的8%
                textAlign = Paint.Align.CENTER
                isAntiAlias = true
                isFakeBoldText = true // 粗体
                setShadowLayer(2f, 1f, 1f, 0xFF000000.toInt()) // 添加阴影
            }

            val cameraName = if (data.connectionId.startsWith("front_camera_")) "前置摄像头" else "后置摄像头"
            canvas.drawText(cameraName, centerX, centerY + bounds.height() * 0.05f, namePaint)

            // 绘制状态文字
            val statusPaint = Paint().apply {
                color = 0xFFFFFF00.toInt() // 黄色
                textSize = Math.min(bounds.width(), bounds.height()) * 0.05f // 状态文字大小为容器的5%
                textAlign = Paint.Align.CENTER
                isAntiAlias = true
                setShadowLayer(1f, 1f, 1f, 0xFF000000.toInt()) // 添加阴影
            }

            canvas.drawText("模拟摄像头容器", centerX, centerY + bounds.height() * 0.15f, statusPaint)

            AppLog.d("【窗口容器View】📷 摄像头占位内容已绘制: ${data.getShortConnectionId()}")

        } catch (e: Exception) {
            AppLog.e("【窗口容器View】绘制摄像头占位内容失败", e)
        }
    }
}
